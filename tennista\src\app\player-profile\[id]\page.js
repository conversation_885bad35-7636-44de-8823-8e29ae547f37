// src/app/player-profile/[id]/page.js
import PlayerCard from "@/components/PlayerCard";
import RecentMatches from "@/components/RecentMatches";
import PlayerStats from "@/components/PlayerStats";
import { supabaseServer } from "@/lib/supabaseServer";

export const dynamic = "force-dynamic";

export default async function PlayerProfilePage({ params }) {
  const id = Number(params.id);
  if (!Number.isFinite(id)) {
    return <main className="p-6">Invalid player id: {params.id}</main>;
  }

  const supabase = supabaseServer();

  // 1) base player info
  const { data: player, error: pErr } = await supabase
    .schema("tennis")
    .from("atp_players")
    .select("player_id, name_first, name_last, hand, dob, ioc")
    .eq("player_id", id)
    .maybeSingle();

  if (pErr) return <main className="p-6">Failed to load player. {pErr.message}</main>;
  if (!player) return <main className="p-6">Player not found.</main>;

// 2) Elo：先查视图，拿 Elo 与 Rank；rank 为空则前端计算
let eloValue = null;
let eloRank = null;

// 2.1 从视图拿最新 Elo + Rank（两列可能是字符串）
const { data: vRow, error: vErr } = await supabase
  .schema("tennis")
  .from("v_current_elo_ranked")
  .select("overall_rating, overall_rank")
  .eq("player_id", id)
  .maybeSingle();

// 注意：v_current_elo_ranked 视图可能有权限问题，所以我们主要依赖 atp_elo_ratings 表

if (vRow && !vErr) {
  eloValue = vRow.overall_rating != null ? Number(vRow.overall_rating) : null;
  eloRank  = vRow.overall_rank  != null ? Number(vRow.overall_rank)  : null;
}

// 2.2 视图没 Elo → 回退到 elo 表最新一条（只有 Elo，没有 Rank）
if (eloValue == null) {
  const { data: tRow } = await supabase
    .schema("tennis")
    .from("atp_elo_ratings")
    .select("overall_rating, rating_date")
    .eq("player_id", id)
    .order("rating_date", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (tRow) {
    eloValue = tRow.overall_rating != null ? Number(tRow.overall_rating) : null;
  }
}

// 2.3 ✨ 兜底：有 Elo 但 rank 仍为空 → 用“比他 Elo 高的人数 + 1”计算名次
if (eloValue != null && (eloRank == null || Number.isNaN(eloRank))) {
  // 调试：先看看数据情况
  console.log("=== Debugging rank calculation ===");
  console.log("Current player Elo:", eloValue);

  // 检查总共有多少条记录
  const { count: totalCount } = await supabase
    .schema("tennis")
    .from("atp_elo_ratings")
    .select("player_id", { count: "exact", head: true });
  console.log("Total records in atp_elo_ratings:", totalCount);

  // 检查有多少不同的球员
  const { data: uniquePlayers } = await supabase
    .schema("tennis")
    .from("atp_elo_ratings")
    .select("player_id")
    .order("player_id");
  const uniquePlayerCount = new Set(uniquePlayers?.map(p => p.player_id)).size;
  console.log("Unique players in atp_elo_ratings:", uniquePlayerCount);

  // 原来的计算方法
  const { count } = await supabase
    .schema("tennis")
    .from("atp_elo_ratings")
    .select("player_id", { count: "exact", head: true })
    .gt("overall_rating", eloValue);
  console.log("Records with higher Elo:", count);

  if (typeof count === "number") {
    eloRank = count + 1;
    console.log("Calculated rank (old method):", eloRank);
  }
}


  // 3) recent matches
  const [
    { data: recentMatches, error: mErr },
    wRes,
    lRes,
  ] = await Promise.all([
    supabase
      .schema("tennis")
      .from("atp_matches")
      .select(`
        match_id,
        tourney_name,
        tourney_date,
        round,
        surface,
        score,
        winner_id,
        winner_name,
        loser_id,
        loser_name
      `)
      .or(`winner_id.eq.${id},loser_id.eq.${id}`)
      .order("tourney_date", { ascending: false })
      .limit(5),

    supabase
      .schema("tennis")
      .from("atp_matches")
      .select("match_id", { count: "exact" })
      .eq("winner_id", id)
      .limit(0),

    supabase
      .schema("tennis")
      .from("atp_matches")
      .select("match_id", { count: "exact" })
      .eq("loser_id", id)
      .limit(0),
  ]);

  const wins = wRes?.count ?? 0;
  const losses = lRes?.count ?? 0;
  const total = wins + losses;

  // 4) age
  const age = player.dob
    ? Math.floor((Date.now() - new Date(player.dob).getTime()) / (365.2425 * 24 * 3600 * 1000))
    : null;

  // 5) player card data
  const playerCardData = {
    id: player.player_id,
    name: `${player.name_first} ${player.name_last}`,
    handed: player.hand,
    country: player.ioc,
    age,
    photo: "/placeholder.png",
    elo: eloValue,                         // number | null
    eloRank: eloRank,                      // number | null
  };

  return (
    <main className="min-h-screen">
      <div className="mx-auto max-w-6xl px-4 py-6 grid gap-6 md:grid-cols-3">
        <section className="md:col-span-1">
          <PlayerCard player={playerCardData} />
        </section>

        <section className="md:col-span-2 space-y-6">
          {recentMatches?.length ? (
            <RecentMatches matches={recentMatches} playerId={id} />
          ) : (
            <div className="text-sm text-slate-500">
              {mErr ? "Failed to load matches." : "No recent matches."}
            </div>
          )}

          <PlayerStats
            stats={[
              { label: "Total Matches", value: total },
              { label: "Wins", value: wins },
              { label: "Losses", value: losses },
              { label: "Current Elo", value: eloValue != null ? Math.round(eloValue) : "N/A" },
              { label: "Elo Rank", value: eloRank != null ? `#${eloRank}` : "N/A" },
            ]}
          />
        </section>
      </div>
    </main>
  );
}
