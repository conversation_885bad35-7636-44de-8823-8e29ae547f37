// Dashboard page
import { supabaseServer } from "@/lib/supabaseServer";
import DashboardCard from "@/components/DashboardCard";
import RecentMatches from "@/components/RecentMatches";
import TopWinners from "@/components/TopWinners";
import EloTop from "@/components/EloTop";

export const dynamic = "force-dynamic";

export default async function DashboardPage() {
  const supabase = supabaseServer();
  const ATP_LEVELS = ["G", "M", "A", "B", "F"];

  const [recentRes, last20Res, eloRes] = await Promise.all([
    supabase
      .schema("tennis")
      .from("atp_matches")
      .select(`
        match_id,
        tourney_name,
        tourney_date,
        round,
        surface,
        score,
        winner_name,
        loser_name,
        winner_id,
        winner_name,
        loser_id,
        loser_name
      `)
      .in("tourney_level", ATP_LEVELS)
      .order("tourney_date", { ascending: false })
      .limit(20),

    supabase
      .schema("tennis")
      .from("atp_matches")
      .select("winner_id, winner_name, tourney_date")
      .order("tourney_date", { ascending: false })
      .limit(20),

    // Try to get top 10 from view first
    supabase
      .schema("tennis")
      .from("v_current_elo_ranked")
      .select("player_id, name_first, name_last, ioc, overall_rating, overall_rank")
      .order("overall_rank", { ascending: true })
      .limit(10),
  ]);

  const recentMatches = recentRes.data ?? [];
  const recentErr = recentRes.error;

  const last20 = last20Res.data ?? [];
  const topWinsErr = last20Res.error;

  let eloTop = eloRes.data ?? [];
  let eloErr = eloRes.error;

  // If view access failed or returned empty data, calculate rankings manually
  if (eloErr || !eloTop.length) {
    console.log("View access failed or empty, calculating rankings manually...");

    try {
      // Get all ratings and calculate latest for each player
      const { data: allRatings } = await supabase
        .schema("tennis")
        .from("atp_elo_ratings")
        .select("player_id, overall_rating, rating_date")
        .order("rating_date", { ascending: false });

      if (allRatings) {
        // Get latest rating for each player
        const latestRatings = new Map();
        for (const rating of allRatings) {
          if (!latestRatings.has(rating.player_id)) {
            latestRatings.set(rating.player_id, {
              player_id: rating.player_id,
              overall_rating: rating.overall_rating,
            });
          }
        }

        // Get player names for the top rated players
        const topPlayerIds = Array.from(latestRatings.values())
          .sort((a, b) => b.overall_rating - a.overall_rating)
          .slice(0, 10)
          .map(p => p.player_id);

        if (topPlayerIds.length > 0) {
          const { data: playerNames } = await supabase
            .schema("tennis")
            .from("atp_players")
            .select("player_id, name_first, name_last, ioc")
            .in("player_id", topPlayerIds);

          // Combine ratings with player names and calculate ranks
          eloTop = topPlayerIds.map((playerId, index) => {
            const rating = latestRatings.get(playerId);
            const playerInfo = playerNames?.find(p => p.player_id === playerId);

            return {
              player_id: playerId,
              name_first: playerInfo?.name_first || "Unknown",
              name_last: playerInfo?.name_last || "",
              ioc: playerInfo?.ioc || null,
              overall_rating: rating.overall_rating,
              overall_rank: index + 1, // Calculate rank based on sorted order
            };
          });

          eloErr = null; // Clear error since we successfully calculated
          console.log("Successfully calculated top 10 rankings manually");
        }
      }
    } catch (error) {
      console.log("Error calculating rankings manually:", error);
      eloErr = error;
    }
  }

  const topWinnersInput = last20.map((m) => ({
    winner_id: m.winner_id,
    winner_name: m.winner_name,
  }));

  return (
    <main className="min-h-screen">
      <div className="mx-auto max-w-6xl px-4 py-6 space-y-6">
        <h1 className="text-2xl font-semibold">Dashboard</h1>

        <section className="grid md:grid-cols-2 gap-6">
          <DashboardCard title="Top 4 players with the highest number of majors won">
            <div className="text-sm text-slate-500">
              Chart placeholder – plug your chart here later.
            </div>
          </DashboardCard>

          <DashboardCard title="Top current players with a high winning streak">
            <div className="text-sm text-slate-500">
              Chart placeholder – plug your chart here later.
            </div>
          </DashboardCard>
        </section>

        <section className="grid md:grid-cols-2 gap-6">

        {recentErr ? (
          <div className="text-sm text-red-600">Failed to load recent matches: {recentErr.message}</div>
        ) : (
          <RecentMatches matches={recentMatches} limit={5} />
        )}


          <DashboardCard
            title="Elo Ratings"
            subtitle="Current top players"
            action={{ label: "View Full Rankings", href: "/rankings" }}
            error={eloErr?.message}
          >
            <EloTop rows={eloTop} />
          </DashboardCard>
        </section>

        <section>
          <DashboardCard
            title="Top Winners in the Last 20 Matches"
            subtitle="Most wins across the latest 20 tour-level matches"
            error={topWinsErr?.message}
          >
            <TopWinners winners={topWinnersInput} />
          </DashboardCard>
        </section>
      </div>
    </main>
  );
}
