// src/app/layout.tsx
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import { ReactNode } from "react";
import { supabaseServer } from "@/lib/supabaseServer";

const geistSans = Geist({ variable: "--font-geist-sans", subsets: ["latin"] });
const geistMono = Geist_Mono({ variable: "--font-geist-mono", subsets: ["latin"] });

export const metadata = {
  title: "Tennista",
  description: "Tennis analytics dashboard",
};

export default async function RootLayout({ children }: { children: ReactNode }) {
  // Get the user from Supabase
  const supabase = supabaseServer();
  let user: any = null;
  try {
    const { data, error } = await supabase.auth.getUser();
    if (!error) user = data.user;
  } catch {
    // ignore
  }

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen
        bg-gradient-to-br from-purple-200 to-indigo-100`}
      >
        {/* Header */}
        <header className="sticky top-0 z-10 bg-white/70 backdrop-blur border-b">
          <div className="mx-auto max-w-6xl px-4 h-14 flex items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2 mr-6 shrink-0">
              <span className="text-2xl">🎾</span>
              <span className="font-semibold">Tennista</span>
            </Link>

            
            <div className="flex-1">
              <Navbar />
            </div>

            
            {user ? (
              <Link
                href="/user"
                className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
              >
                {`Welcome, ${user.user_metadata?.username ?? user.email ?? "User"}`}
              </Link>
            ) : (
              <Link
                href="/auth/login"
                className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
              >
                Sign in
              </Link>
            )}
          </div>
        </header>

        {/* Page content */}
        <main className="mx-auto max-w-6xl px-4 py-6">{children}</main>
      </body>
    </html>
  );
}
