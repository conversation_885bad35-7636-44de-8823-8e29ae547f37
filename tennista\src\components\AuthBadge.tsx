// src/components/AuthBadge.tsx
"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";

export default function AuthBadge() {
  const supabase = createClient();
  const router = useRouter();
  const [email, setEmail] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Read user once and subscribe to auth state changes
  useEffect(() => {
    let mounted = true;

    supabase.auth.getUser().then(({ data: { user } }) => {
      if (!mounted) return;
      setEmail(user?.email ?? null);
      setLoading(false);
    });

    const { data: sub } = supabase.auth.onAuthStateChange((_event, session) => {
      setEmail(session?.user?.email ?? null);
      setLoading(false);
    });

    return () => {
      mounted = false;
      sub.subscription.unsubscribe();
    };
  }, [supabase]);

  const signOut = async () => {
    await supabase.auth.signOut();
    router.replace("/auth/login");
    router.refresh();
  };

  if (loading) {
    return <span className="ml-6 text-sm text-slate-500">Loading…</span>;
  }

  if (!email) {
    return (
      <Link
        href="/auth/login"
        className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
      >
        Sign in
      </Link>
    );
  }

  return (
    <div className="ml-6 flex items-center gap-3">
      <Link
        href="/user"
        className="text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
      >
        {email}
      </Link>
      <button
        onClick={signOut}
        className="text-sm text-slate-600 hover:text-slate-900 underline"
        aria-label="Sign out"
      >
        Sign out
      </button>
    </div>
  );
}
