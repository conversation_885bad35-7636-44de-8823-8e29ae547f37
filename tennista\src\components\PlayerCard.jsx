import Image from "next/image";

export default function PlayerCard({ player }) {
  const { name, age, handed, elo, eloRank, country, photo } = player || {};

  const fmtElo = (v) =>
    typeof v === "number" && !Number.isNaN(v) ? Math.round(v) : "—";

  return (
    <article className="rounded-2xl border bg-white shadow-sm overflow-hidden">
      <div className="aspect-[3/4] relative bg-slate-100">
        <Image
          src={photo || "/placeholder.png"}
          alt={name || "Player"}
          fill
          sizes="(max-width: 768px) 100vw, 33vw"
          className="object-cover"
          priority
        />
      </div>

      <div className="p-4 text-sm space-y-2">
        <h2 className="text-lg font-semibold">Name: {name}</h2>
        <p>Age: {age ?? "—"}</p>
        <p>Handed: {handed ?? "—"}</p>
        <p>Current ELO: {fmtElo(elo)}</p>
        <p>Current ELO Rank: {eloRank ?? "—"}</p>
        <p>Country: {country ?? "—"}</p>
      </div>
    </article>
  );
}
