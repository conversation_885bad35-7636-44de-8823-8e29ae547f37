// Player profile page
import PlayerCard from "@/components/PlayerCard";
import RecentMatches from "@/components/RecentMatches";
import PlayerStats from "@/components/PlayerStats";
import { supabaseServer } from "@/lib/supabaseServer";

export const dynamic = "force-dynamic";

export default async function PlayerProfilePage({ params }) {
  const id = Number(params.id);
  if (!Number.isFinite(id)) {
    return <main className="p-6">Invalid player id: {params.id}</main>;
  }

  const supabase = supabaseServer();

  // 1) Get basic player information
  const { data: player, error: pErr } = await supabase
    .schema("tennis")
    .from("atp_players")
    .select("player_id, name_first, name_last, hand, dob, ioc")
    .eq("player_id", id)
    .maybeSingle();

  if (pErr) return <main className="p-6">Failed to load player. {pErr.message}</main>;
  if (!player) return <main className="p-6">Player not found.</main>;

// 2) Elo rating: First query view to get Elo and rank; calculate rank on frontend if empty
let eloValue = null;
let eloRank = null;

// 2.1 Get latest Elo rating and rank from view (both columns might be strings)
const { data: vRow, error: vErr } = await supabase
  .schema("tennis")
  .from("v_current_elo_ranked")
  .select("overall_rating, overall_rank")
  .eq("player_id", id)
  .maybeSingle();

// Note: v_current_elo_ranked view might have permission issues, so we mainly rely on atp_elo_ratings table

if (vRow && !vErr) {
  eloValue = vRow.overall_rating != null ? Number(vRow.overall_rating) : null;
  eloRank  = vRow.overall_rank  != null ? Number(vRow.overall_rank)  : null;
}

// 2.2 If no Elo data in view, fallback to elo table for latest record (only Elo, no rank)
if (eloValue == null) {
  const { data: tRow } = await supabase
    .schema("tennis")
    .from("atp_elo_ratings")
    .select("overall_rating, rating_date")
    .eq("player_id", id)
    .order("rating_date", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (tRow) {
    eloValue = tRow.overall_rating != null ? Number(tRow.overall_rating) : null;
  }
}

// 2.3 If we have Elo but no rank, calculate rank based on Elo
if (eloValue != null && (eloRank == null || Number.isNaN(eloRank))) {
  // Prioritize using view to calculate rank (view contains current latest Elo for each player)
  try {
    const { count, error: rankErr } = await supabase
      .schema("tennis")
      .from("v_current_elo_ranked")
      .select("player_id", { count: "exact", head: true })
      .gt("overall_rating", eloValue);

    if (typeof count === "number" && !rankErr) {
      eloRank = count + 1;
      console.log("=== Rank from view ===");
      console.log("Players with higher Elo:", count);
      console.log("Calculated rank:", eloRank);
    } else {
      console.log("View access failed, using fallback method...");

      // Fallback method: Calculate rank based on latest Elo for each player
      const { data: allRatings } = await supabase
        .schema("tennis")
        .from("atp_elo_ratings")
        .select("player_id, overall_rating, rating_date")
        .order("rating_date", { ascending: false });

      if (allRatings) {
        // Get latest rating for each player
        const latestRatings = new Map();
        for (const rating of allRatings) {
          if (!latestRatings.has(rating.player_id)) {
            latestRatings.set(rating.player_id, rating.overall_rating);
          }
        }

        // 
        let higherCount = 0;
        for (const rating of latestRatings.values()) {
          if (rating > eloValue) higherCount++;
        }

        eloRank = higherCount + 1;
        console.log("=== Fallback rank calculation ===");
        console.log("Total players:", latestRatings.size);
        console.log("Players with higher Elo:", higherCount);
        console.log("Calculated rank:", eloRank);
      }
    }
  } catch (error) {
    console.log("Error in rank calculation:", error);
  }
}


  // 3) recent matches
  const [
    { data: recentMatches, error: mErr },
    wRes,
    lRes,
  ] = await Promise.all([
    supabase
      .schema("tennis")
      .from("atp_matches")
      .select(`
        match_id,
        tourney_name,
        tourney_date,
        round,
        surface,
        score,
        winner_id,
        winner_name,
        loser_id,
        loser_name
      `)
      .or(`winner_id.eq.${id},loser_id.eq.${id}`)
      .order("tourney_date", { ascending: false })
      .limit(5),

    supabase
      .schema("tennis")
      .from("atp_matches")
      .select("match_id", { count: "exact" })
      .eq("winner_id", id)
      .limit(0),

    supabase
      .schema("tennis")
      .from("atp_matches")
      .select("match_id", { count: "exact" })
      .eq("loser_id", id)
      .limit(0),
  ]);

  const wins = wRes?.count ?? 0;
  const losses = lRes?.count ?? 0;
  const total = wins + losses;

  // 4) age
  const age = player.dob
    ? Math.floor((Date.now() - new Date(player.dob).getTime()) / (365.2425 * 24 * 3600 * 1000))
    : null;

  // 5) player card data
  const playerCardData = {
    id: player.player_id,
    name: `${player.name_first} ${player.name_last}`,
    handed: player.hand,
    country: player.ioc,
    age,
    photo: "/placeholder.png",
    elo: eloValue,                         // number | null
    eloRank: eloRank,                      // number | null
  };

  return (
    <main className="min-h-screen">
      <div className="mx-auto max-w-6xl px-4 py-6 grid gap-6 md:grid-cols-3">
        <section className="md:col-span-1">
          <PlayerCard player={playerCardData} />
        </section>

        <section className="md:col-span-2 space-y-6">
          {recentMatches?.length ? (
            <RecentMatches matches={recentMatches} playerId={id} />
          ) : (
            <div className="text-sm text-slate-500">
              {mErr ? "Failed to load matches." : "No recent matches."}
            </div>
          )}

          <PlayerStats
            stats={[
              { label: "Total Matches", value: total },
              { label: "Wins", value: wins },
              { label: "Losses", value: losses },
              { label: "Current Elo", value: eloValue != null ? Math.round(eloValue) : "N/A" },
              { label: "Elo Rank", value: eloRank != null ? `#${eloRank}` : "N/A" },
            ]}
          />
        </section>
      </div>
    </main>
  );
}
