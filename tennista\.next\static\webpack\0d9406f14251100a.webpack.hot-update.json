{"c": ["app/layout", "webpack"], "r": ["app/player-profile/[id]/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAUFB%5C%5CDesktop%5C%5CIT%5C%5CFIT3164%E2%80%94%E2%80%94project%5C%5CFIT3164_DS_02%5C%5Ctennista%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAUFB%5C%5CDesktop%5C%5CIT%5C%5CFIT3164%E2%80%94%E2%80%94project%5C%5CFIT3164_DS_02%5C%5Ctennista%5C%5Csrc%5C%5Ccomponents%5C%5CRecentMatches.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js"]}